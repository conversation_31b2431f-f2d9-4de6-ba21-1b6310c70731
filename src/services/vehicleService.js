/**
 * Service for vehicle-related API calls
 */

import {getApiUrl} from "../utils/config";
import {getAuthToken} from "../utils/api";

/**
 * Fetches all vehicle makes from the API
 * @returns {Promise<Array>} - Promise resolving to an array of vehicle makes
 */
export const getAllVehicleMakes = async () => {
  try {
    const baseUrl = getApiUrl();
    const token = getAuthToken();

    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    console.log(`Fetching vehicle makes from ${baseUrl}/vehicle-makes/all`);
    const response = await fetch(`${baseUrl}/vehicle-makes/all`, {
      headers
    });

    console.log('API response status:', response.status);

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('API response data:', data);

    // Ensure we always return an array
    const result = Array.isArray(data) ? data : [];
    return result;
  } catch (error) {
    console.error('Error fetching vehicle makes:', error);
    throw error;
  }
};
