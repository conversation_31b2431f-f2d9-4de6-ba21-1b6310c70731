import React, { useState, useEffect, useRef } from 'react';
import { getAllVehicleMakes } from '../services/vehicleService';

import { apiRequest } from '../utils/api';

function EstimateVehicle() {
  const [vehicleMakes, setVehicleMakes] = useState([]);
  const [vehicleModels, setVehicleModels] = useState([]);
  const [selectedMake, setSelectedMake] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchModelTerm, setSearchModelTerm] = useState('');
  const [makeDropdownOpen, setMakeDropdownOpen] = useState(false);
  const [modelDropdownOpen, setModelDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const makeDropdownRef = useRef(null);
  const modelDropdownRef = useRef(null);

  useEffect(() => {
    const fetchVehicleMakes = async () => {
      try {
        setIsLoading(true);
        const data = await getAllVehicleMakes();
        console.log('Vehicle makes data received:', data);
        setVehicleMakes(Array.isArray(data) ? data : []);
        console.log('Vehicle makes state after setting:', Array.isArray(data) ? data : []);
        setError(null);
      } catch (err) {
        setError('Failed to load vehicle makes. Please try again.');
        console.error('Error fetching vehicle makes:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVehicleMakes();
  }, []);

  useEffect(() => {
    // Close dropdowns when clicking outside
    const handleClickOutside = (event) => {
      if (makeDropdownRef.current && !makeDropdownRef.current.contains(event.target)) {
        setMakeDropdownOpen(false);
      }
      if (modelDropdownRef.current && !modelDropdownRef.current.contains(event.target)) {
        setModelDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

      // Add debugging for filtered makes
      const filteredMakes = Array.isArray(vehicleMakes) 
    ? vehicleMakes.filter(make => {
        const included = make && make.name?.toLowerCase().includes(searchTerm.toLowerCase());
        return included;
      })
    : [];

      // Filter models based on search term
      const filteredModels = Array.isArray(vehicleModels) 
    ? vehicleModels.filter(model => {
        const included = model && model.name?.toLowerCase().includes(searchModelTerm.toLowerCase());
        return included;
      })
    : [];

      // Debug filtered makes and models
      console.log('vehicleMakes state:', vehicleMakes);
      console.log('filteredMakes:', filteredMakes);
      console.log('vehicleModels state:', vehicleModels);
      console.log('filteredModels:', filteredModels);

      const handleMakeSelect = async (make) => {
    setSelectedMake(make);
    setSearchTerm(make.name);
    setMakeDropdownOpen(false);

    // Cargar modelos basados en la marca seleccionada desde el API
    setIsLoading(true);
    setVehicleModels([]);
    try {
      const response = await apiRequest(`api/vehicle-models/related-make/${make.id}`, {
        method: 'GET'
      });
      if (Array.isArray(response)) {
        setVehicleModels(response);
      } else {
        console.error('Expected array response for vehicle models but got:', response);
        setVehicleModels([]);
      }
    } catch (err) {
      console.error('Error fetching vehicle models:', err);
      setError('No se pudieron cargar los modelos. Por favor intente nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelSelect = (model) => {
    setSelectedModel(model);
    setSearchModelTerm(model.name);
    setModelDropdownOpen(false);
  };
  return (
    <main>
      <div className="container-fluid px-4">
        <h1 className="mt-4">Cotizar Plan Auto</h1>
        <ol className="breadcrumb mb-4">
          <li className="breadcrumb-item"><a href="#!">Dashboard</a></li>
          <li className="breadcrumb-item active">Cotizar Plan Auto</li>
        </ol>
        <div className="card mb-4">
          <div className="card-header">
            <i className="fas fa-car me-1"></i>
            Cotizar Plan Auto
          </div>
          <div className="card-body">
            <form>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="mb-3" ref={makeDropdownRef}>
                    <label className="form-label">Marca</label>
                    <input 
                      className="form-control" 
                      type="text" 
                      placeholder="Buscar marca" 
                      value={searchTerm}
                      onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setMakeDropdownOpen(true);
                      }}
                      onClick={() => setMakeDropdownOpen(true)}
                    />

                    {makeDropdownOpen && (
                      <div className="dropdown-menu w-100 show position-absolute" style={{ maxHeight: '200px', overflowY: 'auto' }}>
                        {isLoading ? (
                          <div className="dropdown-item">Cargando...</div>
                        ) : error ? (
                          <div className="dropdown-item text-danger">{error}</div>
                        ) : filteredMakes.length > 0 ? (
                          filteredMakes.slice(0, 10).map(make => (
                            <button 
                              key={make.id} 
                              type="button" 
                              className="dropdown-item" 
                              onClick={() => handleMakeSelect(make)}
                            >
                              {make.name?.toUpperCase()}
                            </button>
                          ))
                        ) : (
                          <div className="dropdown-item">No se encontraron coincidencias</div>
                        )}
                      </div>
                    )}
                    <input type="hidden" name="marca" value={selectedMake?.id || ''} />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3" ref={modelDropdownRef}>
                    <label className="form-label">Modelo</label>
                    <input 
                      className="form-control" 
                      type="text" 
                      placeholder="Buscar modelo" 
                      value={searchModelTerm}
                      onChange={(e) => {
                        setSearchModelTerm(e.target.value);
                        setModelDropdownOpen(true);
                      }}
                      onClick={() => setModelDropdownOpen(true)}
                      disabled={!selectedMake}
                    />

                    {modelDropdownOpen && (
                      <div className="dropdown-menu w-100 show position-absolute" style={{ maxHeight: '200px', overflowY: 'auto' }}>
                        {isLoading ? (
                          <div className="dropdown-item">Cargando...</div>
                        ) : !selectedMake ? (
                          <div className="dropdown-item">Primero seleccione una marca</div>
                        ) : filteredModels.length > 0 ? (
                          filteredModels.slice(0, 10).map(model => (
                            <button 
                              key={model.id} 
                              type="button" 
                              className="dropdown-item" 
                              onClick={() => handleModelSelect(model)}
                            >
                              {model.name}
                            </button>
                          ))
                        ) : (
                          <div className="dropdown-item">No se encontraron coincidencias</div>
                        )}
                      </div>
                    )}
                    <input type="hidden" name="modelo" value={selectedModel?.id || ''} />
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <input className="form-control" id="ano" name="ano" type="number" placeholder="Enter year" />
                    <label htmlFor="ano">Año</label>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <input className="form-control" id="suma" name="suma" type="number" placeholder="Enter suma asegurada" />
                    <label htmlFor="suma">Suma Asegurada</label>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <select className="form-select" id="plan" name="plan">
                      <option value="Mensual full" selected>Mensual Full</option>
                      <option value="Anual full">Anual Full</option>
                    </select>
                    <label htmlFor="plan">Plan</label>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <select className="form-select" id="uso" name="uso">
                      <option value="Privado" selected>Privado</option>
                      <option value="Publico">Publico</option>
                    </select>
                    <label htmlFor="uso">Uso</label>
                  </div>
                </div>
              </div>
              <div className="row mb-3">
                <div className="col-md-6">
                  <div className="form-floating mb-3">
                    <select className="form-select" id="estado" name="estado">
                      <option value="Nuevo" selected>Nuevo</option>
                      <option value="Usado">Usado</option>
                    </select>
                    <label htmlFor="estado">Estado</label>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3">
                    <label className="form-label" htmlFor="salvamento">Salvamento</label>
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="salvamento" name="salvamento" />
                      <label className="form-check-label" htmlFor="salvamento">Marcar como salvamento</label>
                    </div>
                  </div>
                </div>
              </div>
              {error && (
                <div className="alert alert-danger" role="alert">
                  {error}
                </div>
              )}
              <div className="d-flex align-items-center justify-content-between mt-4 mb-0">
                <button 
                  className="btn btn-secondary" 
                  type="button" 
                  onClick={() => {
                    // Código para cerrar el formulario o modal si es necesario
                  }}
                >
                  Cerrar
                </button>
                <button 
                  className="btn btn-primary" 
                  type="submit" 
                  disabled={isLoading}
                >
                  {isLoading ? 'Procesando...' : 'Cotizar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  );
}

export default EstimateVehicle;
