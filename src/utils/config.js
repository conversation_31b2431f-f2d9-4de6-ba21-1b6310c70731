// Configuration utility to access environment variables
// This file centralizes all environment variable access

/**
 * Get the API base URL from environment variables
 * @returns {string} The API base URL
 */
export const getApiUrl = () => {
  const apiUrl = process.env.REACT_APP_API_URL;
  
  if (!apiUrl) {
    console.error('REACT_APP_API_URL is not defined in environment variables');
    // Fallback to a default value for development
    return 'http://localhost/api';
  }
  
  return apiUrl;
};

/**
 * Get the application name from environment variables
 * @returns {string} The application name
 */
export const getAppName = () => {
  return process.env.REACT_APP_APP_NAME || 'IT Sentinel';
};

/**
 * Get the application version from environment variables
 * @returns {string} The application version
 */
export const getAppVersion = () => {
  return process.env.REACT_APP_VERSION || '1.0.0';
};

/**
 * Get the environment from environment variables
 * @returns {string} The current environment
 */
export const getEnvironment = () => {
  return process.env.REACT_APP_ENVIRONMENT || 'development';
};

/**
 * Check if we're in development mode
 * @returns {boolean} True if in development mode
 */
export const isDevelopment = () => {
  return getEnvironment() === 'development';
};

/**
 * Log all environment variables for debugging (only in development)
 */
export const logEnvironmentVariables = () => {
  if (isDevelopment()) {
    console.log('=== Environment Variables ===');
    console.log('API URL:', getApiUrl());
    console.log('App Name:', getAppName());
    console.log('Version:', getAppVersion());
    console.log('Environment:', getEnvironment());
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('=============================');
  }
};
